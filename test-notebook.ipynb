import requests
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration

processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base")

# img_url = 'https://storage.googleapis.com/sfr-vision-language-research/BLIP/demo.jpg' 
img_url = "/Users/<USER>/test/hugging-face/image-to-text/image1.jpg"
raw_image = Image.open(requests.get(img_url, stream=True).raw).convert('RGB')

# conditional image captioning
text = "a photography of"
inputs = processor(raw_image, text, return_tensors="pt")

out = model.generate(**inputs)
print(processor.decode(out[0], skip_special_tokens=True))
# >>> a photography of a woman and her dog



# unconditional image captioning
inputs = processor(raw_image, return_tensors="pt")

out = model.generate(**inputs)
print(processor.decode(out[0], skip_special_tokens=True))
# >>> a woman sitting on the beach with her dog

import torch
import requests
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration

# optimize for my apple mps (metal performance shaders) - ie mac GPU
device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")

processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base").to(device)

img_url = 'https://storage.googleapis.com/sfr-vision-language-research/BLIP/demo.jpg' 
raw_image = Image.open(requests.get(img_url, stream=True).raw).convert('RGB')

# Conditional caption
text = "a photography of"
inputs = processor(raw_image, text, return_tensors="pt").to(device)

out = model.generate(**inputs)
print(processor.decode(out[0], skip_special_tokens=True))

# Unconditional caption
inputs = processor(raw_image, return_tensors="pt").to(device)
out = model.generate(**inputs)
print(processor.decode(out[0], skip_special_tokens=True))
b



import torch
import requests
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration

# optimize for my apple mps (metal performance shaders) - ie mac GPU
device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")

processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base").to(device)

img_url = "/Users/<USER>/test/hugging-face/screenshots/image1.png"
raw_image = Image.open(requests.get(img_url, stream=True).raw).convert('RGB')

# Conditional caption
text = "What are the active tabs in the image?"
inputs = processor(raw_image, text, return_tensors="pt").to(device)

out = model.generate(**inputs)
print(processor.decode(out[0], skip_special_tokens=True))



# Unconditional caption
inputs = processor(raw_image, return_tensors="pt").to(device)
out = model.generate(**inputs)
print(processor.decode(out[0], skip_special_tokens=True))


